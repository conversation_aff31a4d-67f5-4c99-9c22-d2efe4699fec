# 视频封边处理工具

基于PySide6和FFmpeg的视频处理工具，支持为竖版1080P视频添加封边装饰、logo水印和文字。

## 功能特性

- 🎥 **视频预览**: 静态帧预览，蓝色进度条控制时间节点
- 🖼️ **封边装饰**: 添加带透明通道的封边图片，自动适配视频尺寸
- 🏷️ **Logo水印**: 可拖拽调整位置，支持鼠标滚轮和拖拽角落缩放
- 📝 **文字叠加**: 支持多行文字，可拖拽调整位置和缩放大小
- 🎬 **视频导出**: 使用FFmpeg高质量导出处理后的视频
- 📐 **智能缩放**: 预览自动等比缩放适配预览框
- 🔧 **交互控制**: 蓝色进度条精确控制视频帧，实时缩放调整

## 系统要求

- Windows 10/11
- Python 3.12
- FFmpeg (路径: `D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin`)

## 安装和使用

### 1. 快速启动
双击 `run.bat` 文件即可自动安装依赖并启动程序。

### 2. 手动安装
```bash
# 安装依赖
pip install -r requirements.txt

# 启动程序
python main.py
```

## 使用说明

### 界面布局
- **预览区域**: 左侧的graphicsView显示视频预览和素材叠加效果
- **进度控制**: 预览区域下方的蓝色进度条，可拖拽选择视频帧
- **控制按钮**:
  - `视频`: 选择要处理的视频文件
  - `封边`: 选择封边装饰图片（PNG格式，支持透明通道）
  - `水印`: 选择logo水印图片
  - `融合`: 开始处理并导出视频
- **文字控制**:
  - `启用文字`: 勾选后显示文字
  - 文字输入框: 输入要添加的文字内容，支持换行

### 操作流程

1. **选择视频**: 点击"视频"按钮，选择竖版1080P视频文件，自动显示第一帧并启用进度条
2. **控制播放**: 拖拽蓝色进度条选择要预览的视频帧，实时显示时间信息
3. **选择封边**: 点击"封边"按钮，选择封边装饰图片，自动适配视频尺寸
4. **添加Logo**: 点击"水印"按钮，选择logo图片，自动显示在预览区域右上角
   - 拖拽调整位置
   - 鼠标滚轮缩放大小
   - 拖拽右下角蓝色手柄缩放
5. **添加文字**: 勾选"启用文字"，在文字框中输入内容，自动显示在预览区域中央
   - 拖拽调整位置
   - 鼠标滚轮缩放字体大小
   - 拖拽右下角蓝色手柄缩放
6. **预览效果**: 在左侧预览区域查看叠加效果，所有元素等比缩放适配预览框
7. **导出视频**: 点击"融合"按钮，处理完成的视频将保存到 `E:\000混剪文件夹\后贴` 目录

### 图层顺序
- **Logo**: 最顶层显示
- **文字**: 第二层显示  
- **封边图**: 第三层显示
- **视频**: 最底层显示

### 交互功能详解

#### 🎯 缩放功能
- **鼠标滚轮缩放**: 选中logo或文字后，滚动鼠标滚轮进行缩放
- **拖拽角落缩放**: 选中元素后，拖拽右下角的蓝色手柄进行缩放
- **缩放范围**: 支持0.1倍到3倍的缩放范围

#### 🎮 进度条控制
- **蓝色进度条**: 美观的蓝色样式进度条
- **精确控制**: 拖拽进度条精确选择视频的任意帧
- **时间显示**: 实时显示当前时间和总时长（格式：MM:SS）

### 性能优化
- 预览使用静态帧显示，无需连续播放
- 智能等比缩放适配预览框大小
- 支持实时拖拽调整位置和缩放
- 自适应字体和logo大小

## 注意事项

1. **视频格式**: 建议使用MP4格式的竖版1080P视频
2. **封边图片**: 必须是PNG格式，支持透明通道，建议尺寸为1080x1920
3. **Logo图片**: 支持PNG/JPG格式，程序会自动调整大小
4. **FFmpeg路径**: 确保FFmpeg安装在指定路径，或修改代码中的路径设置
5. **输出目录**: 确保输出目录 `E:\000混剪文件夹\后贴` 存在写入权限

## 故障排除

### 常见问题

**Q: 程序启动失败**
A: 检查Python 3.12是否正确安装，运行 `python --version` 确认版本

**Q: 视频处理失败**
A: 检查FFmpeg路径是否正确，确保视频文件格式支持

**Q: 预览不显示**
A: 检查视频文件是否损坏，尝试使用其他视频文件

**Q: 拖拽功能不工作**
A: 确保已选择对应的素材文件，logo和文字需要先添加才能拖拽

## 技术架构

- **UI框架**: PySide6 (Qt6)
- **视频处理**: OpenCV + FFmpeg
- **图像处理**: PIL/Pillow
- **多线程**: QThread用于视频预览和处理

## 开发者信息

本工具使用PySide6开发，支持现代化的图形界面和高性能的视频处理功能。
