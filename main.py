# -*- coding: utf-8 -*-
import sys
import os
import subprocess
import threading
import time
from pathlib import Path

from PySide6.QtWidgets import (QApplication, QMainWindow, QFileDialog, 
                               QGraphicsScene, QGraphicsView, QGraphicsPixmapItem,
                               QGraphicsTextItem, QMessageBox, QGraphicsProxyWidget)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QRectF, QPointF
from PySide6.QtGui import QPixmap, QImage, QFont, QPen, QBrush, QColor, QPainter
import cv2
import numpy as np

from ui_main_window import Ui_MainWindow


class DraggableItem(QGraphicsPixmapItem):
    """可拖拽的图形项"""
    def __init__(self, pixmap=None):
        super().__init__(pixmap)
        self.setFlag(QGraphicsPixmapItem.ItemIsMovable, True)
        self.setFlag(QGraphicsPixmapItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsPixmapItem.ItemSendsGeometryChanges, True)


class DraggableTextItem(QGraphicsTextItem):
    """可拖拽的文字项"""
    def __init__(self, text=""):
        super().__init__(text)
        self.setFlag(QGraphicsTextItem.ItemIsMovable, True)
        self.setFlag(QGraphicsTextItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsTextItem.ItemSendsGeometryChanges, True)
        
        # 设置默认字体和颜色
        font = QFont()
        font.setPointSize(24)
        self.setFont(font)
        self.setDefaultTextColor(QColor(0, 0, 0))  # 黑色字体


class VideoPreviewThread(QThread):
    """视频预览线程"""
    frame_ready = Signal(np.ndarray)
    
    def __init__(self, video_path):
        super().__init__()
        self.video_path = video_path
        self.cap = None
        self.running = False
        self.fps = 30
        
    def run(self):
        self.cap = cv2.VideoCapture(self.video_path)
        if not self.cap.isOpened():
            return
            
        # 获取视频FPS，限制预览性能
        original_fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.fps = min(original_fps, 15)  # 限制预览最大15fps
        
        self.running = True
        frame_interval = 1.0 / self.fps
        
        while self.running:
            ret, frame = self.cap.read()
            if not ret:
                # 视频结束，重新开始
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
                
            self.frame_ready.emit(frame)
            time.sleep(frame_interval)
            
    def stop(self):
        self.running = False
        if self.cap:
            self.cap.release()
        self.quit()
        self.wait()


class VideoProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # 设置窗口标题
        self.setWindowTitle("视频封边处理工具")
        
        # 初始化变量
        self.video_path = None
        self.border_path = None
        self.logo_path = None
        self.output_dir = Path("E:/000混剪文件夹/后贴")
        self.ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化图形场景
        self.scene = QGraphicsScene()
        self.ui.graphicsView.setScene(self.scene)
        self.ui.graphicsView.setRenderHint(QPainter.Antialiasing)
        
        # 图层项目
        self.video_item = None
        self.border_item = None
        self.logo_item = None
        self.text_item = None
        
        # 视频预览线程
        self.video_thread = None
        
        # 连接信号槽
        self.setup_connections()
        
        # 设置默认文字
        self.ui.textEdit.setPlainText("请输入要添加的文字")
        
    def setup_connections(self):
        """设置信号槽连接"""
        self.ui.pushButton.clicked.connect(self.select_video)
        self.ui.pushButton_2.clicked.connect(self.select_border)
        self.ui.pushButton_3.clicked.connect(self.select_logo)
        self.ui.pushButton_4.clicked.connect(self.process_video)
        self.ui.checkBox.toggled.connect(self.toggle_text)
        self.ui.textEdit.textChanged.connect(self.update_text)
        
    def select_video(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.video_path = file_path
            self.load_video_preview()
            
    def select_border(self):
        """选择封边图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择封边图片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.border_path = file_path
            self.load_border_image()
            
    def select_logo(self):
        """选择logo水印"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Logo水印", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.logo_path = file_path
            self.load_logo_image()
            
    def load_video_preview(self):
        """加载视频预览"""
        if not self.video_path:
            return
            
        # 停止之前的预览线程
        if self.video_thread:
            self.video_thread.stop()
            
        # 创建新的预览线程
        self.video_thread = VideoPreviewThread(self.video_path)
        self.video_thread.frame_ready.connect(self.update_video_frame)
        self.video_thread.start()
        
        # 获取视频尺寸信息
        cap = cv2.VideoCapture(self.video_path)
        if cap.isOpened():
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()
            
            # 设置场景大小为预览大小
            self.scene.setSceneRect(0, 0, 540, 960)
            
    def update_video_frame(self, frame):
        """更新视频帧"""
        try:
            # 转换OpenCV帧为QPixmap
            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

            # 缩放到预览大小（降低分辨率以提高性能）
            preview_width = 540  # 1080的一半
            preview_height = 960  # 1920的一半
            q_image = q_image.scaled(preview_width, preview_height, Qt.KeepAspectRatio, Qt.FastTransformation)
            pixmap = QPixmap.fromImage(q_image)

            # 更新或创建视频项
            if self.video_item is None:
                self.video_item = QGraphicsPixmapItem(pixmap)
                self.video_item.setZValue(0)  # 最底层
                self.scene.addItem(self.video_item)
            else:
                self.video_item.setPixmap(pixmap)

        except Exception as e:
            print(f"更新视频帧时出错: {e}")
            
    def load_border_image(self):
        """加载封边图片"""
        if not self.border_path:
            return

        pixmap = QPixmap(self.border_path)
        # 缩放到预览大小
        preview_width = 540
        preview_height = 960
        pixmap = pixmap.scaled(preview_width, preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        if self.border_item is None:
            self.border_item = QGraphicsPixmapItem(pixmap)
            self.border_item.setZValue(2)  # 第三层
            self.scene.addItem(self.border_item)
        else:
            self.border_item.setPixmap(pixmap)
            
    def load_logo_image(self):
        """加载logo图片"""
        if not self.logo_path:
            return
            
        pixmap = QPixmap(self.logo_path)
        # 保持原比例，不要太大
        if pixmap.width() > 200 or pixmap.height() > 200:
            pixmap = pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        if self.logo_item is None:
            self.logo_item = DraggableItem(pixmap)
            self.logo_item.setZValue(4)  # 最顶层
            # 默认位置在右上角
            self.logo_item.setPos(800, 100)
            self.scene.addItem(self.logo_item)
        else:
            self.logo_item.setPixmap(pixmap)

    def toggle_text(self, checked):
        """切换文字显示"""
        if checked:
            self.create_text_item()
        else:
            if self.text_item:
                self.scene.removeItem(self.text_item)
                self.text_item = None

    def create_text_item(self):
        """创建文字项"""
        if self.text_item is None:
            text = self.ui.textEdit.toPlainText()
            self.text_item = DraggableTextItem(text)
            self.text_item.setZValue(3)  # 第二层
            # 默认居中位置
            self.text_item.setPos(400, 960)  # 屏幕中央
            self.scene.addItem(self.text_item)

    def update_text(self):
        """更新文字内容"""
        if self.text_item and self.ui.checkBox.isChecked():
            text = self.ui.textEdit.toPlainText()
            self.text_item.setPlainText(text)

    def process_video(self):
        """处理视频"""
        if not self.video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件！")
            return

        if not self.border_path:
            QMessageBox.warning(self, "警告", "请先选择封边图片！")
            return

        # 禁用处理按钮
        self.ui.pushButton_4.setEnabled(False)
        self.ui.pushButton_4.setText("处理中...")

        # 在新线程中处理视频
        threading.Thread(target=self._process_video_thread, daemon=True).start()

    def _process_video_thread(self):
        """视频处理线程"""
        try:
            # 检查FFmpeg是否存在
            if not self.ffmpeg_path.exists():
                QMessageBox.critical(self, "错误", f"FFmpeg未找到！\n请检查路径：{self.ffmpeg_path}")
                return

            # 生成输出文件名
            video_name = Path(self.video_path).stem
            output_path = self.output_dir / f"{video_name}_processed.mp4"

            # 构建FFmpeg命令
            cmd = self._build_ffmpeg_command(output_path)
            print("FFmpeg命令:", " ".join(cmd))  # 调试输出

            # 执行FFmpeg命令
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

            if result.returncode == 0:
                QMessageBox.information(self, "成功", f"视频处理完成！\n输出路径：{output_path}")
            else:
                error_msg = result.stderr if result.stderr else result.stdout
                QMessageBox.critical(self, "错误", f"视频处理失败！\n错误信息：{error_msg}")
                print("FFmpeg错误:", error_msg)  # 调试输出

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理过程中发生错误：{str(e)}")
            print("异常:", str(e))  # 调试输出
        finally:
            # 重新启用处理按钮
            self.ui.pushButton_4.setEnabled(True)
            self.ui.pushButton_4.setText("融合")

    def _build_ffmpeg_command(self, output_path):
        """构建FFmpeg命令"""
        cmd = [str(self.ffmpeg_path), "-i", self.video_path]

        # 添加封边图片
        cmd.extend(["-i", self.border_path])

        # 构建基础滤镜链：视频缩放 + 封边叠加
        filter_complex = "[0:v]scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2[video];[1:v]scale=1080:1920[border];[video][border]overlay=0:0:format=auto,format=yuv420p[tmp]"

        input_count = 2
        current_output = "[tmp]"

        # 添加logo（如果有）
        if self.logo_path and self.logo_item:
            cmd.extend(["-i", self.logo_path])
            logo_x = int(self.logo_item.pos().x())
            logo_y = int(self.logo_item.pos().y())
            filter_complex += f";[{input_count}:v]scale=iw:ih[logo];{current_output}[logo]overlay={logo_x}:{logo_y}[tmp{input_count}]"
            current_output = f"[tmp{input_count}]"
            input_count += 1

        # 添加文字（如果启用）
        if self.ui.checkBox.isChecked() and self.text_item:
            text = self.ui.textEdit.toPlainText().replace("'", "\\'").replace(":", "\\:").replace("\n", "\\n")
            text_x = int(self.text_item.pos().x())
            text_y = int(self.text_item.pos().y())

            text_filter = f"drawtext=text='{text}':fontcolor=black:fontsize=24:x={text_x}:y={text_y}"
            filter_complex += f";{current_output}{text_filter}[final]"
            current_output = "[final]"

        cmd.extend(["-filter_complex", filter_complex])
        cmd.extend(["-map", current_output.strip("[]")])
        cmd.extend(["-map", "0:a?"])  # 复制音频（如果有）
        cmd.extend(["-c:v", "libx264", "-preset", "medium", "-crf", "23"])
        cmd.extend(["-c:a", "aac", "-b:a", "128k"])
        cmd.extend(["-y", str(output_path)])  # 覆盖输出文件

        return cmd

    def closeEvent(self, event):
        """关闭事件"""
        if self.video_thread:
            self.video_thread.stop()
        event.accept()


def main():
    app = QApplication(sys.argv)
    window = VideoProcessor()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
