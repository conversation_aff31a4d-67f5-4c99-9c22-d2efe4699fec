@echo off
echo 启动视频封边处理工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python！请确保Python 3.12已安装并添加到PATH环境变量中。
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show PySide6 >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败！
        pause
        exit /b 1
    )
)

REM 启动应用程序
echo 启动应用程序...
python main.py

pause
