视频封边处理工具 - 详细使用说明
=====================================

🎯 新增功能说明
--------------

1. 【缩放功能】
   - Logo和文字现在支持缩放调整
   - 方法1：选中元素后，使用鼠标滚轮放大/缩小
   - 方法2：选中元素后，拖拽右下角的蓝色缩放手柄
   - 缩放范围：0.1倍 - 3倍

2. 【蓝色进度条】
   - 视频加载后，预览区域下方会出现蓝色进度条
   - 拖拽进度条可以选择显示视频的任意帧
   - 右侧显示当前时间和总时长

🎮 操作指南
-----------

【基本操作流程】
1. 启动程序：双击 run.bat
2. 选择视频：点击"视频"按钮，选择MP4等视频文件
3. 控制帧：拖拽蓝色进度条选择要编辑的视频帧
4. 添加封边：点击"封边"按钮，选择PNG封边图片
5. 添加Logo：点击"水印"按钮，选择logo图片
6. 添加文字：勾选"启用文字"，输入文字内容
7. 调整位置：拖拽logo和文字到合适位置
8. 调整大小：使用滚轮或拖拽蓝色手柄缩放
9. 导出视频：点击"融合"按钮完成处理

【详细交互说明】

Logo操作：
- 选择logo后自动显示在右上角
- 点击选中logo（出现蓝色边框）
- 拖拽移动位置
- 滚轮缩放大小
- 拖拽右下角蓝色手柄精确缩放

文字操作：
- 勾选"启用文字"后自动显示在中央
- 在右侧文本框输入文字内容（支持换行）
- 点击选中文字（出现蓝色边框）
- 拖拽移动位置
- 滚轮缩放字体大小
- 拖拽右下角蓝色手柄精确缩放

进度条操作：
- 加载视频后自动启用
- 拖拽滑块选择视频帧
- 右侧显示时间信息
- 蓝色样式美观易用

🔧 技术特点
-----------

1. 智能缩放：所有元素自动适配预览框大小
2. 实时预览：所有调整立即在预览区域显示
3. 精确控制：支持像素级位置和大小调整
4. 性能优化：静态帧预览，流畅操作体验
5. 用户友好：蓝色主题，直观的视觉反馈

⚠️ 注意事项
-----------

1. 选中元素时会显示蓝色边框和缩放手柄
2. 只有选中的元素才能进行滚轮缩放
3. 进度条只在加载视频后才会启用
4. 缩放范围限制在0.1-3倍之间
5. 文字缩放是调整字体大小，logo缩放是调整图片大小

🎨 界面元素说明
--------------

- 预览区域：显示视频帧和所有叠加元素
- 蓝色进度条：控制视频帧选择
- 时间显示：当前时间/总时长
- 蓝色边框：表示元素被选中
- 蓝色手柄：右下角的缩放控制点
- 控制按钮：视频、封边、水印、融合
- 文字控制：启用复选框和文本输入框

📁 输出说明
-----------

处理完成的视频将保存到：E:\000混剪文件夹\后贴
文件名格式：原视频名_processed.mp4
保持原始音频和高质量视频编码

🔧 最新修复
-----------

v1.1 更新内容：
1. 修复了Qt线程问题 - 视频处理现在在后台线程中进行
2. 改进了中文字符支持 - 使用临时文件处理中文文字
3. 优化了FFmpeg命令构建 - 更稳定的视频处理
4. 增强了错误处理 - 更详细的错误信息显示

中文字符处理：
- 程序会自动创建临时文本文件来处理中文字符
- 如果临时文件创建失败，会自动降级为ASCII字符
- 支持常见的中文标点符号和文字

性能改进：
- 视频处理不再阻塞UI界面
- 处理过程中可以继续操作其他功能
- 更快的响应速度和更好的用户体验
